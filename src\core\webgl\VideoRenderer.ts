/**
 * WebGL视频渲染器
 * 实现离屏video绘制到WebGL的核心渲染引擎
 */

export interface VideoRendererOptions {
  canvas: HTMLCanvasElement
  width: number
  height: number
}

export class VideoRenderer {
  private gl: WebGL2RenderingContext
  private program: WebG<PERSON>rogram
  private vertexBuffer!: WebGLBuffer
  private textureCoordBuffer!: WebGLBuffer
  private textureCache: Map<HTMLVideoElement | HTMLImageElement, WebGLTexture> = new Map()
  private canvas: HTMLCanvasElement
  private width: number
  private height: number

  // Uniform位置
  private uTransformLocation: WebGLUniformLocation | null = null
  private uOpacityLocation: WebGLUniformLocation | null = null
  private uTextureLocation: WebGLUniformLocation | null = null

  // Attribute位置
  private aPositionLocation: number = 0
  private aTexCoordLocation: number = 0

  // 着色器源码
  private vertexShaderSource = `#version 300 es
    in vec2 a_position;
    in vec2 a_texCoord;
    uniform mat3 u_transform;
    out vec2 v_texCoord;

    void main() {
      vec3 position = u_transform * vec3(a_position, 1.0);
      gl_Position = vec4(position.xy, 0.0, 1.0);
      v_texCoord = a_texCoord;
    }
  `

  private fragmentShaderSource = `#version 300 es
    precision mediump float;

    uniform sampler2D u_texture;
    uniform float u_opacity;
    uniform vec2 u_resolution;

    in vec2 v_texCoord;
    out vec4 fragColor;

    void main() {
      vec4 color = texture(u_texture, v_texCoord);
      fragColor = vec4(color.rgb, color.a * u_opacity);
    }
  `

  private textCanvas?: HTMLCanvasElement
  private textContext?: CanvasRenderingContext2D

  constructor(options: VideoRendererOptions) {
    this.canvas = options.canvas
    this.width = options.width
    this.height = options.height

    // 获取WebGL2上下文
    const gl = this.canvas.getContext('webgl2')
    if (!gl) {
      throw new Error('WebGL2 not supported')
    }
    this.gl = gl

    // 设置画布尺寸
    this.canvas.width = this.width
    this.canvas.height = this.height
    this.gl.viewport(0, 0, this.width, this.height)

    // 初始化着色器程序
    this.program = this.createShaderProgram()

    // 获取uniform位置
    this.uTransformLocation = this.gl.getUniformLocation(this.program, 'u_transform')
    this.uOpacityLocation = this.gl.getUniformLocation(this.program, 'u_opacity')
    this.uTextureLocation = this.gl.getUniformLocation(this.program, 'u_texture')

    // 获取attribute位置
    this.aPositionLocation = this.gl.getAttribLocation(this.program, 'a_position')
    this.aTexCoordLocation = this.gl.getAttribLocation(this.program, 'a_texCoord')

    // 创建缓冲区
    this.createBuffers()

    // 纹理将在渲染时按需创建

    // 设置混合模式支持透明度
    this.gl.enable(this.gl.BLEND)
    this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA)
  }

  private createShader(type: number, source: string): WebGLShader {
    const shader = this.gl.createShader(type)
    if (!shader) {
      throw new Error('Failed to create shader')
    }

    this.gl.shaderSource(shader, source)
    this.gl.compileShader(shader)

    if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
      const error = this.gl.getShaderInfoLog(shader)
      this.gl.deleteShader(shader)
      throw new Error(`Shader compilation error: ${error}`)
    }

    return shader
  }

  private createShaderProgram(): WebGLProgram {
    const vertexShader = this.createShader(this.gl.VERTEX_SHADER, this.vertexShaderSource)
    const fragmentShader = this.createShader(this.gl.FRAGMENT_SHADER, this.fragmentShaderSource)

    const program = this.gl.createProgram()
    if (!program) {
      throw new Error('Failed to create shader program')
    }

    this.gl.attachShader(program, vertexShader)
    this.gl.attachShader(program, fragmentShader)
    this.gl.linkProgram(program)

    if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
      const error = this.gl.getProgramInfoLog(program)
      this.gl.deleteProgram(program)
      throw new Error(`Program linking error: ${error}`)
    }

    // 清理着色器
    this.gl.deleteShader(vertexShader)
    this.gl.deleteShader(fragmentShader)

    return program
  }

  private createBuffers(): void {
    // 顶点坐标 (全屏四边形)
    const vertices = new Float32Array([-1.0, -1.0, 1.0, -1.0, -1.0, 1.0, 1.0, 1.0])

    // 纹理坐标
    const texCoords = new Float32Array([0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0])

    // 创建顶点缓冲区
    this.vertexBuffer = this.gl.createBuffer()!
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.vertexBuffer)
    this.gl.bufferData(this.gl.ARRAY_BUFFER, vertices, this.gl.STATIC_DRAW)

    // 创建纹理坐标缓冲区
    this.textureCoordBuffer = this.gl.createBuffer()!
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.textureCoordBuffer)
    this.gl.bufferData(this.gl.ARRAY_BUFFER, texCoords, this.gl.STATIC_DRAW)
  }

  private createTexture(): WebGLTexture {
    const texture = this.gl.createTexture()
    if (!texture) {
      throw new Error('Failed to create texture')
    }

    this.gl.bindTexture(this.gl.TEXTURE_2D, texture)

    // 设置纹理参数
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE)
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE)
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR)
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR)

    return texture
  }

  /**
   * 渲染视频帧到WebGL画布
   * @param video HTML视频元素
   * @param opacity 透明度 (0-1) - 现在固定为1.0
   * @param clearCanvas 是否清空画布 - 现在由外部控制
   * @param transform 视频变换参数
   * @param isRecording 是否正在录制（录制时使用更宽松的要求）
   */
  public renderVideoFrame(
    video: HTMLVideoElement,
    opacity: number = 1.0,
    clearCanvas: boolean = false,
    transform?: {
      x: number
      y: number
      scaleX: number
      scaleY: number
      rotation: number
    },
    isRecording: boolean = false,
  ): void {
    // 检查是否为定格帧
    const isFreezeFrame = (video as any)._isFreezeFrame
    const freezeFrameImage = (video as any)._freezeFrameImage

    if (isFreezeFrame && freezeFrameImage) {
      this.renderImageFrame(freezeFrameImage, opacity, transform)
      return
    }

    // 检查视频是否准备好渲染 - 录制时使用更宽松的要求
    const minReadyState = isRecording ? video.HAVE_METADATA : video.HAVE_CURRENT_DATA
    if (video.readyState < minReadyState) {
      return
    }

    // 检查视频是否有有效的尺寸
    if (video.videoWidth === 0 || video.videoHeight === 0) {
      return
    }

    // 检查视频是否处于错误状态
    if (video.error) {
      return
    }

    // clearCanvas参数现在由外部控制，这里不处理

    // 使用着色器程序
    this.gl.useProgram(this.program)

    // 获取或创建视频纹理，为定格帧使用独立的缓存key
    const cacheKey = (video as any)._isFreezeFrame
      ? `${(video as any)._freezeFrameId || 'freeze'}_${video.src || 'no-src'}`
      : video

    let texture = this.textureCache.get(cacheKey as any)
    if (!texture) {
      texture = this.createTexture()
      this.textureCache.set(cacheKey as any, texture)
    }

    // 激活纹理单元
    this.gl.activeTexture(this.gl.TEXTURE0)

    // 更新视频纹理，添加错误处理
    this.gl.bindTexture(this.gl.TEXTURE_2D, texture)
    try {
      this.gl.texImage2D(
        this.gl.TEXTURE_2D,
        0,
        this.gl.RGBA,
        this.gl.RGBA,
        this.gl.UNSIGNED_BYTE,
        video,
      )
    } catch (error) {
      return
    }

    // 创建变换矩阵
    const transformMatrix = transform
      ? this.createTransformMatrix(
          transform.x,
          transform.y,
          transform.scaleX,
          transform.scaleY,
          transform.rotation,
        )
      : this.createTransformMatrix(0.5, 0.5, 1.0, 1.0, 0) // 默认居中，原始大小

    // 设置uniform变量
    if (this.uTransformLocation) {
      this.gl.uniformMatrix3fv(this.uTransformLocation, false, transformMatrix)
    }
    if (this.uOpacityLocation) {
      this.gl.uniform1f(this.uOpacityLocation, opacity)
    }
    if (this.uTextureLocation) {
      this.gl.uniform1i(this.uTextureLocation, 0)
    }

    // 绑定顶点坐标
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.vertexBuffer)
    this.gl.enableVertexAttribArray(this.aPositionLocation)
    this.gl.vertexAttribPointer(this.aPositionLocation, 2, this.gl.FLOAT, false, 0, 0)

    // 绑定纹理坐标
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.textureCoordBuffer)
    this.gl.enableVertexAttribArray(this.aTexCoordLocation)
    this.gl.vertexAttribPointer(this.aTexCoordLocation, 2, this.gl.FLOAT, false, 0, 0)

    // 确保混合状态正确，支持多轨道透明度混合
    this.gl.enable(this.gl.BLEND)
    this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA)

    // 绘制
    this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4)
  }

  /**
   * 创建变换矩阵
   */
  private createTransformMatrix(
    x: number,
    y: number,
    scaleX: number,
    scaleY: number,
    rotation: number,
  ): Float32Array {
    const cos = Math.cos(rotation)
    const sin = Math.sin(rotation)

    // 将相对坐标(0-1)转换为WebGL坐标(-1到1)
    const translateX = (x - 0.5) * 2
    const translateY = (0.5 - y) * 2 // Y轴翻转

    // 创建变换矩阵 (3x3, 列主序)
    return new Float32Array([
      scaleX * cos,
      scaleY * sin,
      0,
      -scaleX * sin,
      scaleY * cos,
      0,
      translateX,
      translateY,
      1,
    ])
  }

  /**
   * 渲染图片帧（用于定格帧）
   */
  public renderImageFrame(
    image: HTMLImageElement,
    opacity: number = 1.0,
    transform?: {
      x: number
      y: number
      scaleX: number
      scaleY: number
      rotation: number
    },
  ): void {
    // 检查图片是否已加载
    if (!image.complete || image.naturalWidth === 0) {
      return
    }

    // 使用着色器程序
    this.gl.useProgram(this.program)

    // 获取或创建图片纹理，使用图片的src作为唯一key
    const imageKey = `img_${image.src || Date.now()}_${image.naturalWidth}x${image.naturalHeight}`
    let texture = this.textureCache.get(imageKey as any)
    if (!texture) {
      texture = this.createTexture()
      this.textureCache.set(imageKey as any, texture)
    }

    // 激活纹理单元
    this.gl.activeTexture(this.gl.TEXTURE0)

    // 更新图片纹理
    this.gl.bindTexture(this.gl.TEXTURE_2D, texture)
    this.gl.texImage2D(
      this.gl.TEXTURE_2D,
      0,
      this.gl.RGBA,
      this.gl.RGBA,
      this.gl.UNSIGNED_BYTE,
      image,
    )

    // 设置纹理uniform
    if (this.uTextureLocation) {
      this.gl.uniform1i(this.uTextureLocation, 0)
    }

    // 创建变换矩阵
    const transformMatrix = transform
      ? this.createTransformMatrix(
          transform.x,
          transform.y,
          transform.scaleX,
          transform.scaleY,
          transform.rotation,
        )
      : this.createTransformMatrix(0.5, 0.5, 1.0, 1.0, 0) // 默认居中，原始大小

    // 设置uniform变量
    if (this.uTransformLocation) {
      this.gl.uniformMatrix3fv(this.uTransformLocation, false, transformMatrix)
    }
    if (this.uOpacityLocation) {
      this.gl.uniform1f(this.uOpacityLocation, opacity)
    }

    // 绑定顶点缓冲区
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.vertexBuffer)
    this.gl.vertexAttribPointer(this.aPositionLocation, 2, this.gl.FLOAT, false, 0, 0)
    this.gl.enableVertexAttribArray(this.aPositionLocation)

    // 绑定纹理坐标缓冲区
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.textureCoordBuffer)
    this.gl.vertexAttribPointer(this.aTexCoordLocation, 2, this.gl.FLOAT, false, 0, 0)
    this.gl.enableVertexAttribArray(this.aTexCoordLocation)

    // 绘制
    this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4)
  }

  /**
   * 渲染文字片段
   */
  public renderTextClips(textClips: any[]): void {
    if (textClips.length === 0) return

    // 初始化文字画布（如果需要）
    if (!this.textCanvas) {
      this.textCanvas = document.createElement('canvas')
      this.textCanvas.width = this.width
      this.textCanvas.height = this.height
      this.textContext = this.textCanvas.getContext('2d') || undefined
    }

    if (!this.textContext) return

    // 清空文字画布
    this.textContext.clearRect(0, 0, this.width, this.height)

    // 渲染每个文字片段
    textClips.forEach((textClip) => {
      this.renderSingleTextClip(textClip)
    })

    // 将文字画布渲染到WebGL
    this.renderTextCanvasToWebGL()
  }

  private renderSingleTextClip(textClip: any): void {
    if (!this.textContext) return

    const ctx = this.textContext

    // 保存当前状态
    ctx.save()

    // 设置字体样式
    const fontStyle = textClip.fontStyle || 'normal'
    const fontWeight = textClip.fontWeight || 'normal'
    const fontSize = textClip.fontSize || 32
    const fontFamily = textClip.fontFamily || 'Arial'

    ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`
    ctx.fillStyle = textClip.color || '#ffffff'
    ctx.textAlign = textClip.textAlign || 'left'
    ctx.globalAlpha = textClip.opacity || 1.0

    // 计算位置
    const x = textClip.x * this.width
    const y = textClip.y * this.height

    // 应用旋转
    if (textClip.rotation) {
      ctx.translate(x, y)
      ctx.rotate((textClip.rotation * Math.PI) / 180)
      ctx.translate(-x, -y)
    }

    // 渲染文字
    ctx.fillText(textClip.content, x, y)

    // 恢复状态
    ctx.restore()
  }

  private renderTextCanvasToWebGL(): void {
    if (!this.textCanvas) return

    // 创建或更新文字纹理
    const textTexture = this.gl.createTexture()
    if (!textTexture) return

    this.gl.bindTexture(this.gl.TEXTURE_2D, textTexture)
    this.gl.texImage2D(
      this.gl.TEXTURE_2D,
      0,
      this.gl.RGBA,
      this.gl.RGBA,
      this.gl.UNSIGNED_BYTE,
      this.textCanvas,
    )

    // 设置纹理参数
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE)
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE)
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR)
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR)

    // 使用着色器程序渲染文字纹理
    this.gl.useProgram(this.program)

    // 设置变换矩阵（单位矩阵，全屏）
    const transform = new Float32Array([1, 0, 0, 0, 1, 0, 0, 0, 1])

    this.gl.uniformMatrix3fv(this.uTransformLocation, false, transform)
    this.gl.uniform1f(this.uOpacityLocation, 1.0)
    this.gl.uniform1i(this.uTextureLocation, 0)

    // 启用混合以支持透明度
    this.gl.enable(this.gl.BLEND)
    this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA)

    // 绘制
    this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4)

    // 清理纹理，但保持混合状态启用，避免影响后续视频渲染
    this.gl.deleteTexture(textTexture)
    // 不禁用混合，让后续的视频渲染保持正确的混合模式
  }

  /**
   * 清空画布
   */
  public clear(): void {
    this.gl.clearColor(0.0, 0.0, 0.0, 0.0)
    this.gl.clear(this.gl.COLOR_BUFFER_BIT)
  }

  /**
   * 清理指定视频元素的纹理
   */
  public cleanupTexture(video: HTMLVideoElement): void {
    // 清理普通视频纹理
    const texture = this.textureCache.get(video)
    if (texture) {
      this.gl.deleteTexture(texture)
      this.textureCache.delete(video)
    }

    // 如果是定格帧，还需要清理定格帧专用的纹理
    if ((video as any)._isFreezeFrame) {
      const freezeFrameId = (video as any)._freezeFrameId
      if (freezeFrameId) {
        const cacheKey = `${freezeFrameId}_${video.src || 'no-src'}`
        const freezeTexture = this.textureCache.get(cacheKey as any)
        if (freezeTexture) {
          this.gl.deleteTexture(freezeTexture)
          this.textureCache.delete(cacheKey as any)
        }
      }

      // 清理定格帧图片纹理
      const freezeFrameImage = (video as any)._freezeFrameImage
      if (freezeFrameImage) {
        const imageKey = `img_${freezeFrameImage.src || Date.now()}_${freezeFrameImage.naturalWidth}x${freezeFrameImage.naturalHeight}`
        const imageTexture = this.textureCache.get(imageKey as any)
        if (imageTexture) {
          this.gl.deleteTexture(imageTexture)
          this.textureCache.delete(imageKey as any)
        }
      }
    }
  }

  /**
   * 获取画布的MediaStream用于录制
   */
  public captureStream(frameRate?: number): MediaStream {
    return this.canvas.captureStream(frameRate)
  }

  /**
   * 销毁渲染器
   */
  public destroy(): void {
    if (this.vertexBuffer) {
      this.gl.deleteBuffer(this.vertexBuffer)
    }
    if (this.textureCoordBuffer) {
      this.gl.deleteBuffer(this.textureCoordBuffer)
    }
    // 清理所有纹理
    this.textureCache.forEach((texture) => {
      this.gl.deleteTexture(texture)
    })
    this.textureCache.clear()
    if (this.program) {
      this.gl.deleteProgram(this.program)
    }
  }

  /**
   * 调整画布尺寸
   */
  public resize(width: number, height: number): void {
    this.width = width
    this.height = height
    this.canvas.width = width
    this.canvas.height = height
    this.gl.viewport(0, 0, width, height)
  }
}
