import * as Tone from 'tone'

/**
 * 增强的音频处理器 - 解决音频空缺问题
 * 使用 Tone.js 提供更稳定的音频处理和同步
 */
export class EnhancedAudioProcessor {
  private toneContext: Tone.BaseContext
  private masterGain: Tone.Gain
  private destination: Tone.Destination
  private audioSources: Map<string, Tone.Player> = new Map()
  private gainNodes: Map<string, Tone.Gain> = new Map()
  private isInitialized = false
  private outputStream: MediaStream | null = null
  private mediaStreamDestination: MediaStreamAudioDestinationNode | null = null

  constructor() {
    this.toneContext = Tone.getContext()
    this.masterGain = new Tone.Gain(1.0)
    this.destination = Tone.getDestination()

    // 连接主增益到输出
    this.masterGain.connect(this.destination)
  }

  /**
   * 初始化音频处理器
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      // 确保 Tone.js 上下文启动
      if (this.toneContext.state !== 'running') {
        await this.toneContext.resume()
      }

      // 创建 MediaStream 输出
      this.createMediaStreamOutput()

      this.isInitialized = true
    } catch (error) {
      throw error
    }
  }

  /**
   * 创建 MediaStream 输出
   */
  private createMediaStreamOutput(): void {
    try {
      // 使用原生 AudioContext 创建 MediaStreamDestination
      const nativeContext = this.toneContext.rawContext as AudioContext
      this.mediaStreamDestination = nativeContext.createMediaStreamDestination()

      // 连接 Tone.js 的主输出到 MediaStreamDestination
      this.masterGain.connect(this.mediaStreamDestination as any)

      this.outputStream = this.mediaStreamDestination.stream
    } catch (error) {}
  }

  /**
   * 添加音频源
   */
  public async addAudioSource(
    id: string,
    audioUrl: string,
    options: {
      startTime: number
      duration: number
      trimStart?: number
      trimEnd?: number
      volume?: number
      loop?: boolean
    },
  ): Promise<void> {
    try {
      // 创建 Tone.Player
      const player = new Tone.Player({
        url: audioUrl,
        loop: options.loop || false,
        autostart: false,
        onload: () => {},
      })

      // 创建增益节点
      const gainNode = new Tone.Gain(options.volume || 1.0)

      // 连接音频链
      player.connect(gainNode)
      gainNode.connect(this.masterGain)

      // 存储引用
      this.audioSources.set(id, player)
      this.gainNodes.set(id, gainNode)

      // 等待音频加载完成
      await new Promise<void>((resolve, reject) => {
        const checkLoaded = () => {
          if (player.loaded) {
            resolve()
          } else {
            setTimeout(checkLoaded, 100)
          }
        }

        // 设置超时
        setTimeout(() => {
          if (!player.loaded) {
            reject(new Error(`Audio source ${id} failed to load within timeout`))
          }
        }, 10000)

        checkLoaded()
      })
    } catch (error) {
      throw error
    }
  }

  /**
   * 移除音频源
   */
  public removeAudioSource(id: string): void {
    const player = this.audioSources.get(id)
    const gainNode = this.gainNodes.get(id)

    if (player) {
      player.stop()
      player.dispose()
      this.audioSources.delete(id)
    }

    if (gainNode) {
      gainNode.dispose()
      this.gainNodes.delete(id)
    }
  }

  /**
   * 播放指定时间点的音频
   */
  public async playAtTime(currentTime: number, activeClipIds: string[]): Promise<void> {
    try {
      // 停止所有当前播放的音频
      this.stopAll()

      // 等待短暂时间确保停止完成
      await new Promise((resolve) => setTimeout(resolve, 50))

      // 播放活跃的音频片段
      for (const clipId of activeClipIds) {
        const player = this.audioSources.get(clipId)
        if (player && player.loaded) {
          try {
            // 计算音频内的播放位置
            const audioTime = this.calculateAudioTime(clipId, currentTime)

            if (audioTime >= 0 && audioTime < player.buffer.duration) {
              // 设置播放位置并开始播放
              player.start(0, audioTime)
            }
          } catch (error) {}
        }
      }
    } catch (error) {}
  }

  /**
   * 停止所有音频播放
   */
  public stopAll(): void {
    this.audioSources.forEach((player, id) => {
      try {
        if (player.state === 'started') {
          player.stop()
        }
      } catch (error) {}
    })
  }

  /**
   * 设置音频源音量
   */
  public setVolume(id: string, volume: number): void {
    const gainNode = this.gainNodes.get(id)
    if (gainNode) {
      gainNode.gain.value = volume
    }
  }

  /**
   * 设置主音量
   */
  public setMasterVolume(volume: number): void {
    this.masterGain.gain.value = volume
  }

  /**
   * 获取输出流
   */
  public getOutputStream(): MediaStream | null {
    return this.outputStream
  }

  /**
   * 计算音频内的播放时间
   */
  private calculateAudioTime(clipId: string, currentTime: number): number {
    // 这里需要根据实际的片段信息计算
    // 暂时返回 currentTime，实际使用时需要传入片段信息
    return currentTime
  }

  /**
   * 创建音频缓冲区
   */
  public async createAudioBuffer(audioUrl: string): Promise<AudioBuffer> {
    try {
      const response = await fetch(audioUrl)
      const arrayBuffer = await response.arrayBuffer()
      const audioBuffer = await this.toneContext.rawContext.decodeAudioData(arrayBuffer)
      return audioBuffer
    } catch (error) {
      throw error
    }
  }

  /**
   * 销毁音频处理器
   */
  public dispose(): void {
    // 停止所有音频
    this.stopAll()

    // 清理所有音频源
    this.audioSources.forEach((player, id) => {
      this.removeAudioSource(id)
    })

    // 清理 Tone.js 节点
    this.masterGain.dispose()

    // 清理 MediaStream
    if (this.outputStream) {
      this.outputStream.getTracks().forEach((track) => track.stop())
      this.outputStream = null
    }

    this.isInitialized = false
  }
}
