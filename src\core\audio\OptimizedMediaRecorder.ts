/**
 * 优化的 MediaRecorder - 解决音频编码和兼容性问题
 */
export interface RecorderOptions {
  videoBitsPerSecond?: number
  audioBitsPerSecond?: number
  preferredVideoCodec?: string
  preferredAudioCodec?: string
  enableAudioProcessing?: boolean
}

export interface SupportedFormat {
  mimeType: string
  videoCodec: string
  audioCodec: string
  priority: number
  description: string
}

export class OptimizedMediaRecorder {
  private mediaRecorder: MediaRecorder | null = null
  private recordedChunks: Blob[] = []
  private isRecording = false
  private supportedFormats: SupportedFormat[] = []

  constructor() {
    this.detectSupportedFormats()
  }

  /**
   * 检测支持的音视频格式
   */
  private detectSupportedFormats(): void {
    // 定义优先级格式列表（按兼容性和质量排序）
    const formatCandidates: SupportedFormat[] = [
      // MP4 + H.264 + AAC (最佳兼容性)
      {
        mimeType: 'video/mp4;codecs="avc1.42E01E,mp4a.40.2"',
        videoCodec: 'H.264 Baseline',
        audioCodec: 'AAC-LC',
        priority: 1,
        description: 'MP4 + H.264 Baseline + AAC-LC (最佳兼容性)',
      },
      {
        mimeType: 'video/mp4;codecs="avc1.42001E,mp4a.40.2"',
        videoCodec: 'H.264 Baseline',
        audioCodec: 'AAC-LC',
        priority: 2,
        description: 'MP4 + H.264 Baseline + AAC-LC (备选)',
      },
      {
        mimeType: 'video/mp4;codecs="avc1.4D401E,mp4a.40.2"',
        videoCodec: 'H.264 Main',
        audioCodec: 'AAC-LC',
        priority: 3,
        description: 'MP4 + H.264 Main + AAC-LC',
      },

      // WebM + VP8/VP9 + Vorbis (开源格式)
      {
        mimeType: 'video/webm;codecs="vp8,vorbis"',
        videoCodec: 'VP8',
        audioCodec: 'Vorbis',
        priority: 4,
        description: 'WebM + VP8 + Vorbis',
      },
      {
        mimeType: 'video/webm;codecs="vp9,vorbis"',
        videoCodec: 'VP9',
        audioCodec: 'Vorbis',
        priority: 5,
        description: 'WebM + VP9 + Vorbis',
      },

      // WebM + H.264 + AAC (混合格式)
      {
        mimeType: 'video/webm;codecs="avc1.42E01E,mp4a.40.2"',
        videoCodec: 'H.264',
        audioCodec: 'AAC',
        priority: 6,
        description: 'WebM + H.264 + AAC',
      },

      // 基础格式（无编解码器指定）
      {
        mimeType: 'video/mp4',
        videoCodec: 'Default',
        audioCodec: 'Default',
        priority: 7,
        description: 'MP4 (默认编解码器)',
      },
      {
        mimeType: 'video/webm',
        videoCodec: 'Default',
        audioCodec: 'Default',
        priority: 8,
        description: 'WebM (默认编解码器)',
      },
    ]

    // 检测支持的格式
    this.supportedFormats = formatCandidates.filter((format) => {
      const isSupported = MediaRecorder.isTypeSupported(format.mimeType)
      return isSupported
    })
  }

  /**
   * 获取最佳支持的格式
   */
  public getBestSupportedFormat(): SupportedFormat | null {
    return this.supportedFormats.length > 0 ? this.supportedFormats[0] : null
  }

  /**
   * 获取所有支持的格式
   */
  public getSupportedFormats(): SupportedFormat[] {
    return [...this.supportedFormats]
  }

  /**
   * 创建优化的 MediaRecorder
   */
  public createRecorder(stream: MediaStream, options: RecorderOptions = {}): MediaRecorder {
    const bestFormat = this.getBestSupportedFormat()

    if (!bestFormat) {
      throw new Error('没有找到支持的音视频格式')
    }

    // 优化的录制参数
    const recorderOptions: MediaRecorderOptions = {
      mimeType: bestFormat.mimeType,
      videoBitsPerSecond: options.videoBitsPerSecond || this.getOptimalVideoBitrate(bestFormat),
      audioBitsPerSecond: options.audioBitsPerSecond || this.getOptimalAudioBitrate(bestFormat),
    }

    try {
      this.mediaRecorder = new MediaRecorder(stream, recorderOptions)
      this.setupRecorderEvents()
      return this.mediaRecorder
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取最优视频比特率
   */
  private getOptimalVideoBitrate(format: SupportedFormat): number {
    // 根据格式和编解码器调整比特率
    if (format.videoCodec.includes('H.264')) {
      return 2500000 // 2.5 Mbps for H.264
    } else if (format.videoCodec.includes('VP9')) {
      return 2000000 // 2.0 Mbps for VP9
    } else if (format.videoCodec.includes('VP8')) {
      return 3000000 // 3.0 Mbps for VP8
    }
    return 2500000 // 默认值
  }

  /**
   * 获取最优音频比特率
   */
  private getOptimalAudioBitrate(format: SupportedFormat): number {
    // 根据音频编解码器调整比特率
    if (format.audioCodec.includes('AAC')) {
      return 128000 // 128 kbps for AAC
    } else if (format.audioCodec.includes('Vorbis')) {
      return 192000 // 192 kbps for Vorbis
    }
    return 128000 // 默认值
  }

  /**
   * 设置录制器事件
   */
  private setupRecorderEvents(): void {
    if (!this.mediaRecorder) return

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data && event.data.size > 0) {
        this.recordedChunks.push(event.data)
      }
    }

    this.mediaRecorder.onstart = () => {
      this.isRecording = true
      this.recordedChunks = []
    }

    this.mediaRecorder.onstop = () => {
      this.isRecording = false
    }

    this.mediaRecorder.onerror = (event) => {
      this.isRecording = false
    }

    this.mediaRecorder.onpause = () => {}

    this.mediaRecorder.onresume = () => {}
  }

  /**
   * 开始录制
   */
  public startRecording(timeslice?: number): void {
    if (!this.mediaRecorder) {
      throw new Error('MediaRecorder 未初始化')
    }

    if (this.isRecording) {
      return
    }

    try {
      // 使用较小的时间片以获得更好的音频连续性
      const optimalTimeslice = timeslice || 100 // 100ms 时间片
      this.mediaRecorder.start(optimalTimeslice)
    } catch (error) {
      throw error
    }
  }

  /**
   * 停止录制
   */
  public stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder) {
        reject(new Error('MediaRecorder 未初始化'))
        return
      }

      if (!this.isRecording) {
        reject(new Error('当前没有在录制'))
        return
      }

      const originalOnStop = this.mediaRecorder.onstop

      this.mediaRecorder.onstop = () => {
        // 调用原始的 onstop 处理器
        if (originalOnStop) {
          originalOnStop.call(this.mediaRecorder!, new Event('stop'))
        }

        // 创建最终的 Blob
        const mimeType = this.mediaRecorder?.mimeType || 'video/mp4'
        const blob = new Blob(this.recordedChunks, { type: mimeType })

        // 清理
        this.recordedChunks = []

        if (blob.size === 0) {
          reject(new Error('录制的视频为空'))
        } else {
          resolve(blob)
        }
      }

      try {
        this.mediaRecorder.stop()
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 获取录制状态
   */
  public getRecordingState(): {
    isRecording: boolean
    state: string
    chunksCount: number
    totalSize: number
  } {
    return {
      isRecording: this.isRecording,
      state: this.mediaRecorder?.state || 'inactive',
      chunksCount: this.recordedChunks.length,
      totalSize: this.recordedChunks.reduce((sum, chunk) => sum + chunk.size, 0),
    }
  }

  /**
   * 销毁录制器
   */
  public dispose(): void {
    if (this.mediaRecorder && this.isRecording) {
      try {
        this.mediaRecorder.stop()
      } catch (error) {}
    }

    this.mediaRecorder = null
    this.recordedChunks = []
    this.isRecording = false
  }
}
