{"name": "video-editor", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:with-typecheck": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/three": "^0.178.1", "element-plus": "^2.10.4", "pinia": "^3.0.3", "three": "^0.178.0", "tone": "^15.1.22", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^5.0.0", "@vitest/ui": "^3.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^5.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}}