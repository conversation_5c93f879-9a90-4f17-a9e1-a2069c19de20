export interface TextClip {
  id: string
  startTime: number
  duration: number
  content: string
  fontSize: number
  fontFamily: string
  color: string
  x: number // 相对位置 0-1
  y: number // 相对位置 0-1
  opacity: number
  rotation: number
  textAlign: 'left' | 'center' | 'right'
  fontWeight: 'normal' | 'bold'
  fontStyle: 'normal' | 'italic'
}

export class TextTrack {
  private clips: TextClip[] = []

  addClip(clip: TextClip): void {
    this.clips.push(clip)
  }

  removeClip(clipId: string): void {
    const index = this.clips.findIndex((clip) => clip.id === clipId)
    if (index !== -1) {
      this.clips.splice(index, 1)
    }
  }

  updateClip(clipId: string, updates: Partial<TextClip>): void {
    const clip = this.clips.find((c) => c.id === clipId)
    if (clip) {
      Object.assign(clip, updates)
    }
  }

  getActiveClips(currentTime: number): TextClip[] {
    return this.clips.filter(
      (clip) => currentTime >= clip.startTime && currentTime < clip.startTime + clip.duration,
    )
  }

  getAllClips(): TextClip[] {
    return [...this.clips]
  }

  getDuration(): number {
    if (this.clips.length === 0) return 0
    return Math.max(...this.clips.map((clip) => clip.startTime + clip.duration))
  }
}
