# 定格功能使用指南

## 功能概述

定格功能允许你从视频片段中提取当前播放帧，并将其转换为一个可调整时长的静态图片素材。这个功能对于创建暂停效果、强调特定画面或制作图片蒙太奇非常有用。

## 如何使用定格功能

### 1. 准备工作
- 确保你已经导入了视频文件到素材库
- 将视频片段拖拽到时间轴上

### 2. 创建定格帧
1. **选择素材**: 点击时间轴上的视频片段来选中它
2. **定位时间**: 使用播放控制或直接点击时间轴来定位到你想要定格的帧
3. **创建定格**: 点击时间轴控制面板中的 "📸 定格" 按钮

### 3. 定格帧特性
- **视觉标识**: 定格帧在时间轴上显示为红色背景，带有虚线边框和📸图标
- **默认时长**: 新创建的定格帧默认持续3秒
- **位置**: 定格帧会被添加到与源视频相同的轨道上，起始时间为当前播放时间

### 4. 调整定格帧
当选中定格帧时，工具栏会显示时长调整控件：
- **时长输入框**: 可以输入0.1到60秒之间的任意值
- **实时更新**: 修改时长后，时间轴上的定格帧长度会立即更新

### 5. 定格帧的行为
- **静态显示**: 定格帧始终显示相同的图像，不会随时间变化
- **变换支持**: 支持位置调整、缩放等变换操作
- **层级渲染**: 按照轨道顺序正确渲染（track-1在底层，track-2在上层）

## 技术实现

### 核心流程
1. **帧捕获**: 从源视频的当前时间点捕获帧数据
2. **图像转换**: 将视频帧转换为静态图像
3. **素材创建**: 创建新的视频片段，使用静态图像作为源
4. **渲染优化**: 使用专门的图像渲染路径，避免视频解码开销

### 关键组件
- `VideoEditor.createFreezeFrame()`: 核心定格创建逻辑
- `VideoRenderer.renderImageFrame()`: 专门的图像渲染方法
- UI控制面板: 时长调整和视觉反馈

## 使用技巧

### 最佳实践
1. **精确定位**: 使用逐帧播放或暂停功能来精确定位到想要的帧
2. **合理时长**: 根据内容需要设置合适的定格时长
3. **层级管理**: 利用多轨道功能创建复杂的定格效果

### 创意应用
- **动作定格**: 在动作高潮时创建定格效果
- **转场效果**: 使用定格帧作为场景转换的过渡
- **画面强调**: 突出重要的视觉元素
- **时间操控**: 创建时间暂停的视觉效果

## 注意事项

1. **性能考虑**: 大量定格帧可能影响渲染性能
2. **内存使用**: 每个定格帧都会在内存中保存图像数据
3. **浏览器兼容**: 功能依赖现代浏览器的Canvas和WebGL支持

## 故障排除

### 常见问题
- **定格按钮禁用**: 确保已选中视频片段
- **定格帧不显示**: 检查浏览器控制台是否有错误信息
- **时长调整无效**: 确保输入的值在有效范围内(0.1-60秒)

### 调试信息
定格功能会在浏览器控制台输出详细的调试信息，包括：
- 定格帧创建过程
- 图像捕获状态
- 渲染路径选择

## 未来改进

计划中的功能增强：
- 批量定格创建
- 定格帧预览缩略图
- 更多定格效果选项
- 导出定格帧为独立图片
