/**
 * 音频质量检测器 - 检测和修复音频空缺问题
 */
export interface AudioQualityReport {
  hasAudio: boolean
  silentSegments: Array<{ start: number; end: number; duration: number }>
  averageVolume: number
  peakVolume: number
  dynamicRange: number
  clippingDetected: boolean
  qualityScore: number // 0-100
  issues: string[]
  recommendations: string[]
}

export interface AudioAnalysisOptions {
  silenceThreshold?: number // 静音阈值 (0-1)
  silenceDuration?: number // 最小静音持续时间 (秒)
  sampleRate?: number
  fftSize?: number
}

export class AudioQualityDetector {
  private audioContext: AudioContext
  private analyser: AnalyserNode
  private dataArray: Uint8Array
  private isAnalyzing = false

  constructor(audioContext?: AudioContext) {
    this.audioContext = audioContext || new AudioContext()
    this.analyser = this.audioContext.createAnalyser()
    this.analyser.fftSize = 2048
    this.dataArray = new Uint8Array(this.analyser.frequencyBinCount)
  }

  /**
   * 分析音频流质量
   */
  public async analyzeAudioStream(
    stream: MediaStream,
    duration: number = 5,
    options: AudioAnalysisOptions = {},
  ): Promise<AudioQualityReport> {
    const { silenceThreshold = 0.01, silenceDuration = 0.5, sampleRate = 44100 } = options

    try {
      // 连接音频流到分析器
      const source = this.audioContext.createMediaStreamSource(stream)
      source.connect(this.analyser)

      // 收集音频数据
      const samples: number[] = []
      const volumeHistory: number[] = []
      const startTime = performance.now()
      const sampleInterval = 50 // 50ms 采样间隔

      this.isAnalyzing = true

      await new Promise<void>((resolve) => {
        const collectSample = () => {
          if (!this.isAnalyzing) {
            resolve()
            return
          }

          const elapsed = (performance.now() - startTime) / 1000
          if (elapsed >= duration) {
            this.isAnalyzing = false
            resolve()
            return
          }

          // 获取频域数据
          this.analyser.getByteFrequencyData(this.dataArray)

          // 计算音量
          const volume = this.calculateVolume(this.dataArray)
          volumeHistory.push(volume)
          samples.push(...Array.from(this.dataArray))

          setTimeout(collectSample, sampleInterval)
        }

        collectSample()
      })

      // 断开连接
      source.disconnect()

      // 分析收集的数据
      const report = this.analyzeAudioData(volumeHistory, {
        silenceThreshold,
        silenceDuration,
        sampleInterval: sampleInterval / 1000,
      })

      return report
    } catch (error) {
      throw error
    }
  }

  /**
   * 分析音频文件质量
   */
  public async analyzeAudioFile(audioBuffer: AudioBuffer): Promise<AudioQualityReport> {
    try {
      // 获取音频数据
      const channelData = audioBuffer.getChannelData(0) // 使用第一个声道
      const sampleRate = audioBuffer.sampleRate

      // 计算音量历史
      const volumeHistory: number[] = []
      const windowSize = Math.floor(sampleRate * 0.05) // 50ms 窗口

      for (let i = 0; i < channelData.length; i += windowSize) {
        const window = channelData.slice(i, i + windowSize)
        const rms = this.calculateRMS(window)
        volumeHistory.push(rms)
      }

      const report = this.analyzeAudioData(volumeHistory, {
        silenceThreshold: 0.01,
        silenceDuration: 0.5,
        sampleInterval: 0.05,
      })

      return report
    } catch (error) {
      throw error
    }
  }

  /**
   * 实时监控音频质量
   */
  public startRealTimeMonitoring(
    stream: MediaStream,
    callback: (report: Partial<AudioQualityReport>) => void,
    interval: number = 1000,
  ): () => void {
    const source = this.audioContext.createMediaStreamSource(stream)
    source.connect(this.analyser)

    this.isAnalyzing = true
    const volumeHistory: number[] = []

    const monitor = () => {
      if (!this.isAnalyzing) return

      this.analyser.getByteFrequencyData(this.dataArray)
      const volume = this.calculateVolume(this.dataArray)
      volumeHistory.push(volume)

      // 保持最近的历史记录
      if (volumeHistory.length > 100) {
        volumeHistory.shift()
      }

      // 生成实时报告
      const report: Partial<AudioQualityReport> = {
        hasAudio: volume > 0.01,
        averageVolume: volumeHistory.reduce((a, b) => a + b, 0) / volumeHistory.length,
        peakVolume: Math.max(...volumeHistory),
        qualityScore: this.calculateQualityScore(volumeHistory),
      }

      callback(report)
      setTimeout(monitor, interval)
    }

    monitor()

    // 返回停止函数
    return () => {
      this.isAnalyzing = false
      source.disconnect()
    }
  }

  /**
   * 分析音频数据
   */
  private analyzeAudioData(
    volumeHistory: number[],
    options: {
      silenceThreshold: number
      silenceDuration: number
      sampleInterval: number
    },
  ): AudioQualityReport {
    const { silenceThreshold, silenceDuration, sampleInterval } = options

    // 检测静音段
    const silentSegments = this.detectSilentSegments(
      volumeHistory,
      silenceThreshold,
      silenceDuration,
      sampleInterval,
    )

    // 计算统计信息
    const nonZeroVolumes = volumeHistory.filter((v) => v > 0)
    const averageVolume =
      nonZeroVolumes.length > 0
        ? nonZeroVolumes.reduce((a, b) => a + b, 0) / nonZeroVolumes.length
        : 0
    const peakVolume = Math.max(...volumeHistory)
    const minVolume = Math.min(...nonZeroVolumes)
    const dynamicRange = peakVolume - minVolume

    // 检测削波
    const clippingDetected = volumeHistory.some((v) => v > 0.95)

    // 生成问题和建议
    const issues: string[] = []
    const recommendations: string[] = []

    if (!volumeHistory.some((v) => v > silenceThreshold)) {
      issues.push('未检测到音频信号')
      recommendations.push('检查音频源和连接')
    }

    if (silentSegments.length > 0) {
      issues.push(`检测到 ${silentSegments.length} 个静音段`)
      recommendations.push('检查音频连续性和同步')
    }

    if (averageVolume < 0.1) {
      issues.push('音频音量过低')
      recommendations.push('增加音频增益或检查音量设置')
    }

    if (clippingDetected) {
      issues.push('检测到音频削波')
      recommendations.push('降低音频音量以避免失真')
    }

    if (dynamicRange < 0.1) {
      issues.push('动态范围过小')
      recommendations.push('检查音频压缩设置')
    }

    // 计算质量分数
    const qualityScore = this.calculateQualityScore(volumeHistory)

    return {
      hasAudio: volumeHistory.some((v) => v > silenceThreshold),
      silentSegments,
      averageVolume,
      peakVolume,
      dynamicRange,
      clippingDetected,
      qualityScore,
      issues,
      recommendations,
    }
  }

  /**
   * 检测静音段
   */
  private detectSilentSegments(
    volumeHistory: number[],
    threshold: number,
    minDuration: number,
    sampleInterval: number,
  ): Array<{ start: number; end: number; duration: number }> {
    const segments: Array<{ start: number; end: number; duration: number }> = []
    let silentStart = -1

    for (let i = 0; i < volumeHistory.length; i++) {
      const time = i * sampleInterval
      const volume = volumeHistory[i]

      if (volume <= threshold) {
        // 开始静音段
        if (silentStart === -1) {
          silentStart = time
        }
      } else {
        // 结束静音段
        if (silentStart !== -1) {
          const duration = time - silentStart
          if (duration >= minDuration) {
            segments.push({
              start: silentStart,
              end: time,
              duration,
            })
          }
          silentStart = -1
        }
      }
    }

    // 处理结尾的静音段
    if (silentStart !== -1) {
      const endTime = volumeHistory.length * sampleInterval
      const duration = endTime - silentStart
      if (duration >= minDuration) {
        segments.push({
          start: silentStart,
          end: endTime,
          duration,
        })
      }
    }

    return segments
  }

  /**
   * 计算音量
   */
  private calculateVolume(dataArray: Uint8Array): number {
    let sum = 0
    for (let i = 0; i < dataArray.length; i++) {
      sum += dataArray[i]
    }
    return sum / (dataArray.length * 255) // 归一化到 0-1
  }

  /**
   * 计算 RMS
   */
  private calculateRMS(samples: Float32Array): number {
    let sum = 0
    for (let i = 0; i < samples.length; i++) {
      sum += samples[i] * samples[i]
    }
    return Math.sqrt(sum / samples.length)
  }

  /**
   * 计算质量分数
   */
  private calculateQualityScore(volumeHistory: number[]): number {
    if (volumeHistory.length === 0) return 0

    let score = 100

    // 检查是否有音频
    const hasAudio = volumeHistory.some((v) => v > 0.01)
    if (!hasAudio) score -= 50

    // 检查音量稳定性
    const nonZeroVolumes = volumeHistory.filter((v) => v > 0)
    if (nonZeroVolumes.length > 0) {
      const avg = nonZeroVolumes.reduce((a, b) => a + b, 0) / nonZeroVolumes.length
      const variance =
        nonZeroVolumes.reduce((sum, v) => sum + Math.pow(v - avg, 2), 0) / nonZeroVolumes.length
      const stability = Math.max(0, 1 - variance * 10)
      score *= stability
    }

    // 检查静音比例
    const silentRatio = volumeHistory.filter((v) => v <= 0.01).length / volumeHistory.length
    score *= 1 - silentRatio * 0.5

    return Math.max(0, Math.min(100, score))
  }

  /**
   * 销毁检测器
   */
  public dispose(): void {
    this.isAnalyzing = false
  }
}
