/// <reference types="vite/client" />

// Extend HTMLVideoElement to include vendor-specific audio detection properties
declare global {
  interface HTMLVideoElement {
    // Modern standard property (may not be available in all browsers yet)
    audioTracks?: AudioTrackList;
    // Firefox-specific property
    mozHasAudio?: boolean;
    // WebKit-specific property
    webkitAudioDecodedByteCount?: number;
  }

  // AudioTrackList interface for modern browsers
  interface AudioTrackList {
    readonly length: number;
    [index: number]: AudioTrack;
  }

  interface AudioTrack {
    readonly id: string;
    readonly kind: string;
    readonly label: string;
    readonly language: string;
    enabled: boolean;
  }
}
