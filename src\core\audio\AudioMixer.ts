/**
 * 音频混合器
 * 使用Web Audio API实现多音频轨道的实时混合和处理
 */

import { EnhancedAudioProcessor } from './EnhancedAudioProcessor'
import { AudioBufferManager, type AudioClipInfo } from './AudioBufferManager'
import { AudioQualityDetector } from './AudioQualityDetector'

export interface AudioClip {
  id: string
  audioElement: HTMLAudioElement
  startTime: number // 在时间轴上的开始时间（秒）
  duration: number // 片段持续时间（秒）
  trimStart: number // 音频内部裁剪开始时间（秒）
  trimEnd: number // 音频内部裁剪结束时间（秒）
  volume: number // 音量 0-1
  muted: boolean // 是否静音
  fadeIn: number // 淡入时间（秒）
  fadeOut: number // 淡出时间（秒）
  effects: AudioEffect[] // 应用的效果
  trackId?: string // 所属轨道ID，用于录制时过滤
}

export interface AudioEffect {
  id: string
  type: 'reverb' | 'delay' | 'distortion' | 'filter' | 'compressor' | 'equalizer'
  parameters: Record<string, number>
  enabled: boolean
}

export class AudioMixer {
  private audioContext: AudioContext
  private masterGainNode: GainNode
  private clips: AudioClip[] = []
  private sourceNodes: Map<string, MediaElementAudioSourceNode> = new Map()
  private gainNodes: Map<string, GainNode> = new Map()
  private effectNodes: Map<string, AudioNode[]> = new Map()
  private destination: MediaStreamAudioDestinationNode
  private currentTime: number = 0
  private isPlaying: boolean = false
  private lastUpdateTime: number = 0
  private updateDebounceDelay: number = 50 // 50ms防抖延迟
  private pendingUpdate: number | null = null
  private recordingMode: boolean = false // 录制模式，只处理轨道二音频

  // 增强的音频处理组件
  private enhancedProcessor: EnhancedAudioProcessor | null = null
  private bufferManager: AudioBufferManager | null = null
  private qualityDetector: AudioQualityDetector | null = null
  private useEnhancedProcessing: boolean = false

  constructor() {
    try {
      // 创建音频上下文
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (!AudioContextClass) {
        throw new Error('AudioContext not supported')
      }

      this.audioContext = new AudioContextClass()

      // 检查必要的方法是否存在
      if (!this.audioContext.createMediaElementSource) {
        throw new Error('createMediaElementSource not supported')
      }

      // 创建主增益节点
      this.masterGainNode = this.audioContext.createGain()

      // 创建目标节点用于录制
      this.destination = this.audioContext.createMediaStreamDestination()

      // 连接主增益节点到目标
      this.masterGainNode.connect(this.destination)
      this.masterGainNode.connect(this.audioContext.destination)
    } catch (error) {
      // 创建一个空的音频上下文作为后备
      this.audioContext = null as any
      this.masterGainNode = null as any
      this.destination = null as any
    }
  }

  /**
   * 添加音频片段
   */
  public addClip(clip: AudioClip): void {
    // 检查是否已存在相同ID的片段，避免重复添加
    const existingClip = this.clips.find((c) => c.id === clip.id)
    if (existingClip) {
      return
    }

    // 按开始时间排序插入
    const insertIndex = this.clips.findIndex((c) => c.startTime > clip.startTime)
    if (insertIndex === -1) {
      this.clips.push(clip)
    } else {
      this.clips.splice(insertIndex, 0, clip)
    }

    // 如果AudioContext可用，创建音频节点链
    if (this.audioContext) {
      try {
        this.createAudioNodes(clip)
      } catch (error) {
        // 继续执行，但不使用Web Audio API
      }
    } else {
      // 继续执行，但不使用Web Audio API
    }
  }

  /**
   * 移除音频片段
   */
  public removeClip(clipId: string): boolean {
    const index = this.clips.findIndex((c) => c.id === clipId)
    if (index !== -1) {
      this.disconnectAudioNodes(clipId)
      this.clips.splice(index, 1)
      return true
    }
    return false
  }

  /**
   * 创建音频节点链
   */
  private createAudioNodes(clip: AudioClip): void {
    if (!this.audioContext) {
      throw new Error('AudioContext not available')
    }

    // 创建媒体元素音频源节点
    const sourceNode = this.audioContext.createMediaElementSource(clip.audioElement)
    this.sourceNodes.set(clip.id, sourceNode)

    // 创建增益节点
    const gainNode = this.audioContext.createGain()
    gainNode.gain.value = clip.muted ? 0 : clip.volume
    this.gainNodes.set(clip.id, gainNode)

    // 创建效果节点链
    const effectNodes = this.createEffectChain(clip.effects)
    this.effectNodes.set(clip.id, effectNodes)

    // 连接节点链
    let currentNode: AudioNode = sourceNode

    // 连接效果链
    effectNodes.forEach((effectNode) => {
      currentNode.connect(effectNode)
      currentNode = effectNode
    })

    // 连接到增益节点
    currentNode.connect(gainNode)

    // 连接到主增益节点
    gainNode.connect(this.masterGainNode)

    // 设置音频元素属性
    this.setupAudioElement(clip.audioElement)
  }

  /**
   * 创建效果链
   */
  private createEffectChain(effects: AudioEffect[]): AudioNode[] {
    const nodes: AudioNode[] = []

    effects.forEach((effect) => {
      if (!effect.enabled) return

      let effectNode: AudioNode

      switch (effect.type) {
        case 'reverb':
          effectNode = this.createReverbNode(effect.parameters)
          break
        case 'delay':
          effectNode = this.createDelayNode(effect.parameters)
          break
        case 'distortion':
          effectNode = this.createDistortionNode(effect.parameters)
          break
        case 'filter':
          effectNode = this.createFilterNode(effect.parameters)
          break
        case 'compressor':
          effectNode = this.createCompressorNode(effect.parameters)
          break
        case 'equalizer':
          effectNode = this.createEqualizerNode(effect.parameters)
          break
        default:
          return
      }

      nodes.push(effectNode)
    })

    return nodes
  }

  /**
   * 创建混响效果节点
   */
  private createReverbNode(params: Record<string, number>): ConvolverNode {
    const convolver = this.audioContext.createConvolver()

    // 创建简单的冲激响应
    const length = this.audioContext.sampleRate * (params.roomSize || 2)
    const impulse = this.audioContext.createBuffer(2, length, this.audioContext.sampleRate)

    for (let channel = 0; channel < 2; channel++) {
      const channelData = impulse.getChannelData(channel)
      for (let i = 0; i < length; i++) {
        channelData[i] = (Math.random() * 2 - 1) * Math.pow(1 - i / length, params.decay || 2)
      }
    }

    convolver.buffer = impulse
    return convolver
  }

  /**
   * 创建延迟效果节点
   */
  private createDelayNode(params: Record<string, number>): DelayNode {
    const delay = this.audioContext.createDelay(params.maxDelay || 1)
    delay.delayTime.value = params.delayTime || 0.3
    return delay
  }

  /**
   * 创建失真效果节点
   */
  private createDistortionNode(params: Record<string, number>): WaveShaperNode {
    const waveshaper = this.audioContext.createWaveShaper()
    const amount = params.amount || 50
    const samples = 44100
    const curve = new Float32Array(samples)
    const deg = Math.PI / 180

    for (let i = 0; i < samples; i++) {
      const x = (i * 2) / samples - 1
      curve[i] = ((3 + amount) * x * 20 * deg) / (Math.PI + amount * Math.abs(x))
    }

    waveshaper.curve = curve
    waveshaper.oversample = '4x'
    return waveshaper
  }

  /**
   * 创建滤波器节点
   */
  private createFilterNode(params: Record<string, number>): BiquadFilterNode {
    const filter = this.audioContext.createBiquadFilter()
    filter.type = (params.type as unknown as BiquadFilterType) || 'lowpass'
    filter.frequency.value = params.frequency || 1000
    filter.Q.value = params.Q || 1
    filter.gain.value = params.gain || 0
    return filter
  }

  /**
   * 创建压缩器节点
   */
  private createCompressorNode(params: Record<string, number>): DynamicsCompressorNode {
    const compressor = this.audioContext.createDynamicsCompressor()
    compressor.threshold.value = params.threshold || -24
    compressor.knee.value = params.knee || 30
    compressor.ratio.value = params.ratio || 12
    compressor.attack.value = params.attack || 0.003
    compressor.release.value = params.release || 0.25
    return compressor
  }

  /**
   * 创建均衡器节点
   */
  private createEqualizerNode(params: Record<string, number>): AudioNode {
    // 创建多个滤波器组成均衡器
    const filters: BiquadFilterNode[] = []
    const frequencies = [60, 170, 350, 1000, 3500, 10000]

    frequencies.forEach((freq, index) => {
      const filter = this.audioContext.createBiquadFilter()
      filter.type =
        index === 0 ? 'lowshelf' : index === frequencies.length - 1 ? 'highshelf' : 'peaking'
      filter.frequency.value = freq
      filter.Q.value = 1
      filter.gain.value = params[`band${index}`] || 0
      filters.push(filter)
    })

    // 连接滤波器链
    for (let i = 0; i < filters.length - 1; i++) {
      filters[i].connect(filters[i + 1])
    }

    return filters[0] // 返回第一个滤波器作为输入节点
  }

  /**
   * 断开音频节点连接
   */
  private disconnectAudioNodes(clipId: string): void {
    const sourceNode = this.sourceNodes.get(clipId)
    const gainNode = this.gainNodes.get(clipId)
    const effectNodes = this.effectNodes.get(clipId)

    if (sourceNode && typeof sourceNode.disconnect === 'function') {
      sourceNode.disconnect()
      this.sourceNodes.delete(clipId)
    }

    if (gainNode && typeof gainNode.disconnect === 'function') {
      gainNode.disconnect()
      this.gainNodes.delete(clipId)
    }

    if (effectNodes) {
      effectNodes.forEach((node) => {
        if (node && typeof node.disconnect === 'function') {
          node.disconnect()
        }
      })
      this.effectNodes.delete(clipId)
    }
  }

  /**
   * 设置音频元素属性 - 优化线上环境兼容性
   */
  private setupAudioElement(audio: HTMLAudioElement): void {
    try {
      audio.preload = 'auto' // 改为auto，确保音频完全加载
      audio.muted = false // 确保不静音
      audio.volume = 1.0 // 确保音量为最大

      // 设置crossOrigin以确保AudioContext可以访问
      // 在线上环境中这很重要
      if (!audio.crossOrigin) {
        audio.crossOrigin = 'anonymous'
      }

      // 强制加载音频数据
      audio.load()
    } catch (error) {}
  }

  /**
   * 暂停所有音频片段
   */
  public pauseAll(): void {
    // 标记为强制暂停状态
    this.isPlaying = false

    this.clips.forEach((clip) => {
      if (!clip.audioElement.paused) {
        clip.audioElement.pause()
      }
    })
  }

  /**
   * 更新当前时间并控制音频播放
   */
  public updateTime(time: number, isPlaying: boolean): void {
    this.currentTime = time
    this.isPlaying = isPlaying

    // 清除任何待处理的更新，避免状态冲突
    if (this.pendingUpdate) {
      clearTimeout(this.pendingUpdate)
      this.pendingUpdate = null
    }

    // 立即更新音频状态，不使用防抖
    this.immediateUpdateAudioState(time, isPlaying)
  }

  /**
   * 为播放准备音频（确保音频真正准备好）- 优化录制时的音频预加载
   */
  public async prepareForPlayback(time: number): Promise<void> {
    this.currentTime = time
    this.isPlaying = true

    const activeClips = this.getActiveClips(time)
    const audioPromises: Promise<void>[] = []

    // 为每个活跃的音频片段准备播放
    activeClips.forEach((clip) => {
      const relativeTime = time - clip.startTime
      const audioTime = clip.trimStart + relativeTime

      // 创建音频准备的Promise - 优化录制时的音频预加载
      const audioPromise = new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          resolve() // 超时也resolve，不阻塞录制
        }, 5000) // 增加到5秒超时，给音频更多加载时间

        const onCanPlay = () => {
          clearTimeout(timeout)
          clip.audioElement.removeEventListener('canplay', onCanPlay)
          clip.audioElement.removeEventListener('loadeddata', onLoadedData)
          clip.audioElement.removeEventListener('error', onError)
          resolve()
        }

        const onLoadedData = () => {
          // 数据加载完成，也算准备好
          if (clip.audioElement.readyState >= 2) {
            onCanPlay()
          }
        }

        const onError = () => {
          clearTimeout(timeout)
          clip.audioElement.removeEventListener('canplay', onCanPlay)
          clip.audioElement.removeEventListener('loadeddata', onLoadedData)
          clip.audioElement.removeEventListener('error', onError)

          resolve() // 错误也resolve，不阻塞录制
        }

        // 检查音频元素是否已经准备好
        if (clip.audioElement.readyState >= 2) {
          // 已经有足够的数据，直接设置时间
          try {
            if (Math.abs(clip.audioElement.currentTime - audioTime) > 0.1) {
              clip.audioElement.currentTime = audioTime
            }

            clearTimeout(timeout)
            resolve()
          } catch (error) {
            clearTimeout(timeout)
            resolve()
          }
        } else {
          // 监听多个事件确保音频准备完成
          clip.audioElement.addEventListener('canplay', onCanPlay, { once: true })
          clip.audioElement.addEventListener('loadeddata', onLoadedData, { once: true })
          clip.audioElement.addEventListener('error', onError, { once: true })

          // 强制重新加载音频
          clip.audioElement.load()

          // 设置音频时间
          try {
            if (Math.abs(clip.audioElement.currentTime - audioTime) > 0.1) {
              clip.audioElement.currentTime = audioTime
            }
          } catch (error) {}
        }
      })

      audioPromises.push(audioPromise)
    })

    // 等待所有音频准备完成
    try {
      await Promise.all(audioPromises)
    } catch (error) {
      // 即使有些音频失败，也继续播放
    }

    // 音频准备完成后，应用效果并开始播放
    activeClips.forEach((clip) => {
      const relativeTime = time - clip.startTime

      // 应用淡入淡出效果
      this.applyFadeEffect(clip, relativeTime)

      // 开始播放
      if (clip.audioElement.paused) {
        this.playAudioElementSafely(clip)
      }
    })
  }

  /**
   * 立即更新音频状态（内部方法）
   */
  private immediateUpdateAudioState(time: number, isPlaying: boolean): void {
    const activeClips = this.getActiveClips(time)

    // 创建活跃片段ID集合，用于快速查找
    const activeClipIds = new Set(activeClips.map((clip) => clip.id))

    // 第一步：处理所有音频片段的状态
    this.clips.forEach((clip) => {
      const isActive = activeClipIds.has(clip.id)

      if (isActive && isPlaying) {
        // 活跃片段且应该播放
        const relativeTime = time - clip.startTime
        const audioTime = clip.trimStart + relativeTime

        // 同步设置播放时间
        if (Math.abs(clip.audioElement.currentTime - audioTime) > 0.1) {
          try {
            // 直接同步设置音频时间
            clip.audioElement.currentTime = audioTime
          } catch (error) {
            return
          }
        }

        // 检查是否仍然应该播放
        if (!this.isPlaying) {
          return // 如果已经暂停，不要播放
        }

        // 应用淡入淡出效果
        this.applyFadeEffect(clip, relativeTime)

        // 只有在暂停状态时才播放，避免重复播放
        if (clip.audioElement.paused) {
          this.playAudioElementSafely(clip)
        }
      } else {
        // 非活跃片段或应该暂停，确保暂停
        if (!clip.audioElement.paused) {
          clip.audioElement.pause()
        }
      }
    })
  }

  /**
   * 应用淡入淡出效果
   */
  private applyFadeEffect(clip: AudioClip, relativeTime: number): void {
    const gainNode = this.gainNodes.get(clip.id)
    if (!gainNode) return

    let volume = clip.muted ? 0 : clip.volume

    // 淡入效果
    if (clip.fadeIn > 0 && relativeTime < clip.fadeIn) {
      volume *= relativeTime / clip.fadeIn
    }

    // 淡出效果
    if (clip.fadeOut > 0 && relativeTime > clip.duration - clip.fadeOut) {
      const fadeOutProgress = (clip.duration - relativeTime) / clip.fadeOut
      volume *= fadeOutProgress
    }

    gainNode.gain.value = volume
  }

  /**
   * 安全播放音频元素（处理线上环境的自动播放策略）- 优化录制时的稳定性
   */
  private async playAudioElementSafely(clip: AudioClip): Promise<void> {
    try {
      const audio = clip.audioElement

      // 确保音频元素处于正确状态
      audio.muted = false
      audio.volume = clip.muted ? 0 : clip.volume
      audio.playbackRate = 1.0

      // 检查音频是否已经在播放
      if (!audio.paused) {
        return
      }

      // 确保音频数据已加载
      if (audio.readyState < audio.HAVE_CURRENT_DATA) {
        await new Promise((resolve) => {
          const onCanPlay = () => {
            audio.removeEventListener('canplay', onCanPlay)
            resolve(undefined)
          }
          audio.addEventListener('canplay', onCanPlay, { once: true })

          // 设置超时避免无限等待
          setTimeout(() => {
            audio.removeEventListener('canplay', onCanPlay)
            resolve(undefined)
          }, 1000)
        })
      }

      await audio.play()

      // 验证播放状态
      setTimeout(() => {
        if (audio.paused) {
          audio.play().catch(() => {
            // 静默处理重试失败
          })
        }
      }, 100)
    } catch (error: any) {
      if (error.name === 'NotAllowedError') {
        // 在线上环境中，自动播放可能被阻止，这是正常的
        // 用户交互后音频会正常播放
      } else if (error.name === 'NotSupportedError') {
      } else {
        // 尝试重新播放一次
        setTimeout(() => {
          if (clip.audioElement.paused) {
            clip.audioElement.play().catch(() => {
              // 第二次失败就放弃
            })
          }
        }, 200)
      }
    }
  }

  /**
   * 设置录制模式（只处理轨道二的音频）
   */
  public setRecordingMode(enabled: boolean): void {
    this.recordingMode = enabled
  }

  /**
   * 获取指定时间点的活跃音频片段
   */
  public getActiveClips(time: number): AudioClip[] {
    let activeClips = this.clips.filter((clip) => {
      const clipEndTime = clip.startTime + clip.duration
      return time >= clip.startTime && time < clipEndTime
    })

    // 如果是录制模式，只返回轨道二的音频片段
    if (this.recordingMode) {
      const originalCount = activeClips.length

      activeClips.forEach((clip, index) => {})

      activeClips = activeClips.filter((clip) => {
        // 优先使用trackId字段
        if (clip.trackId) {
          const isTrack2 = clip.trackId === 'track-2'

          return isTrack2
        }
        // 备选方案：检查ID中是否包含track-2标识
        const hasTrack2InId = clip.id.includes('track-2')
        const isTrack2Audio = this.isTrack2AudioClip(clip.id)
        const result = hasTrack2InId || isTrack2Audio
        return result
      })
    }

    return activeClips
  }

  /**
   * 检查音频片段是否属于轨道二
   */
  private isTrack2AudioClip(clipId: string): boolean {
    // 如果clipId以_audio结尾，检查对应的视频片段是否在轨道二
    if (clipId.endsWith('_audio')) {
      return true // 临时返回true，让所有音频片段都通过过滤
    }
    return false
  }

  /**
   * 获取音频片段数量
   */
  public getClipCount(): number {
    return this.clips.length
  }

  /**
   * 获取所有音频片段的状态信息（用于调试）
   */
  public getClipsStatus(): Array<{
    id: string
    startTime: number
    duration: number
    currentTime: number
    paused: boolean
    volume: number
    muted: boolean
  }> {
    return this.clips.map((clip) => ({
      id: clip.id,
      startTime: clip.startTime,
      duration: clip.duration,
      currentTime: clip.audioElement.currentTime,
      paused: clip.audioElement.paused,
      volume: clip.volume,
      muted: clip.muted,
    }))
  }

  /**
   * 设置主音量
   */
  public setMasterVolume(volume: number): void {
    if (this.masterGainNode) {
      this.masterGainNode.gain.value = Math.max(0, Math.min(1, volume))
    }
  }

  /**
   * 获取输出音频流
   */
  public getOutputStream(): MediaStream | null {
    if (this.destination) {
      const stream = this.destination.stream
      return stream
    }
    return null
  }

  /**
   * 强制获取输出音频流，即使没有音频片段也创建空流
   */
  public getOutputStreamForced(): MediaStream | null {
    if (!this.destination) {
      this.reinitializeDestination()
    }

    if (this.destination) {
      const stream = this.destination.stream

      // 如果没有音频轨道但有destination，创建一个低音量轨道并刷新流
      if (stream.getAudioTracks().length === 0 && this.audioContext) {
        this.createSilentAudioTrack()

        // 重新获取流，因为可能需要刷新
        const refreshedStream = this.destination.stream
        return refreshedStream
      }

      return stream
    }

    return null
  }

  /**
   * 强制激活所有音频轨道用于录制
   */
  public forceActivateTracksForRecording(): void {
    // 确保AudioContext正在运行
    if (this.audioContext && this.audioContext.state === 'suspended') {
      this.audioContext.resume()
    }

    this.clips.forEach((clip) => {
      try {
        // 确保音频元素未静音且音量正常
        clip.audioElement.muted = false
        clip.audioElement.volume = clip.volume

        // 预加载音频数据
        if (clip.audioElement.readyState < 2) {
          clip.audioElement.load()
        }

        // 设置音频时间到正确位置
        const currentTime = this.currentTime
        if (currentTime >= clip.startTime && currentTime < clip.startTime + clip.duration) {
          const relativeTime = currentTime - clip.startTime
          const audioTime = clip.trimStart + relativeTime
          clip.audioElement.currentTime = audioTime

          // 强制播放音频元素
          clip.audioElement.play().catch((error) => {})
        }

        // 更新增益节点
        const gainNode = this.gainNodes.get(clip.id)
        if (gainNode) {
          gainNode.gain.value = clip.muted ? 0 : clip.volume
        }
      } catch (error) {}
    })

    // 创建一个持续的音频源确保流保持活跃
    this.ensureStreamActive()
  }

  /**
   * 持续同步所有音频轨道用于录制（专门用于多视频场景）
   */
  public maintainRecordingSync(): void {
    if (!this.isPlaying) return

    const currentTime = this.currentTime
    const activeClips = this.getActiveClips(currentTime)

    // 确保所有应该播放的音频片段都在播放
    activeClips.forEach((clip) => {
      try {
        const relativeTime = currentTime - clip.startTime
        const audioTime = clip.trimStart + relativeTime

        // 确保音频元素时间同步
        if (Math.abs(clip.audioElement.currentTime - audioTime) > 0.2) {
          clip.audioElement.currentTime = audioTime
        }

        // 确保音频正在播放
        if (clip.audioElement.paused) {
          clip.audioElement.play().catch(() => {
            // 静默处理播放失败
          })
        }

        // 确保音量正确
        clip.audioElement.volume = clip.muted ? 0 : clip.volume

        // 更新Web Audio API节点
        const gainNode = this.gainNodes.get(clip.id)
        if (gainNode) {
          gainNode.gain.value = clip.muted ? 0 : clip.volume
        }
      } catch (error) {
        // 静默处理错误，不影响其他音频轨道
      }
    })

    // 暂停不应该播放的音频片段
    this.clips.forEach((clip) => {
      if (!activeClips.includes(clip) && !clip.audioElement.paused) {
        clip.audioElement.pause()
      }
    })
  }

  /**
   * 确保音频流保持活跃
   */
  private ensureStreamActive(): void {
    if (!this.audioContext || !this.destination) return

    try {
      // 创建一个极低音量的持续音源
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()

      oscillator.frequency.value = 20 // 低频，人耳几乎听不到
      gainNode.gain.value = 0.0001 // 极低音量

      oscillator.connect(gainNode)
      gainNode.connect(this.destination)

      oscillator.start()
    } catch (error) {}
  }

  /**
   * 重新初始化destination
   */
  private reinitializeDestination(): void {
    if (this.audioContext && this.masterGainNode) {
      try {
        this.destination = this.audioContext.createMediaStreamDestination()
        this.masterGainNode.connect(this.destination)
      } catch (error) {}
    }
  }

  /**
   * 创建静音音频轨道
   */
  private createSilentAudioTrack(): void {
    if (!this.audioContext || !this.destination) return

    try {
      // 创建一个低音量的振荡器作为占位符（不是完全静音）
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()

      oscillator.frequency.value = 440
      gainNode.gain.value = 0.001 // 极低音量但不是0

      oscillator.connect(gainNode)
      gainNode.connect(this.destination)

      oscillator.start()
    } catch (error) {}
  }

  /**
   * 更新片段音量
   */
  public updateClipVolume(clipId: string, volume: number): void {
    const clip = this.clips.find((c) => c.id === clipId)
    const gainNode = this.gainNodes.get(clipId)

    if (clip && gainNode) {
      clip.volume = Math.max(0, Math.min(1, volume))
      gainNode.gain.value = clip.muted ? 0 : clip.volume
    }
  }

  /**
   * 切换片段静音状态
   */
  public toggleClipMute(clipId: string): void {
    const clip = this.clips.find((c) => c.id === clipId)
    const gainNode = this.gainNodes.get(clipId)

    if (clip && gainNode) {
      clip.muted = !clip.muted
      gainNode.gain.value = clip.muted ? 0 : clip.volume
    }
  }

  /**
   * 根据ID查找音频片段
   */
  public findClipById(clipId: string): AudioClip | null {
    return this.clips.find((c) => c.id === clipId) || null
  }

  /**
   * 更新音频片段的开始时间
   */
  public updateClipStartTime(clipId: string, newStartTime: number): boolean {
    const clip = this.clips.find((c) => c.id === clipId)
    if (clip) {
      clip.startTime = newStartTime
      // 重新排序片段数组
      this.clips.sort((a, b) => a.startTime - b.startTime)
      return true
    }
    return false
  }

  /**
   * 更新音频片段的裁剪时间
   */
  public updateClipTrimTimes(clipId: string, trimStart: number, trimEnd: number): boolean {
    const clip = this.clips.find((c) => c.id === clipId)
    if (clip) {
      clip.trimStart = trimStart
      clip.trimEnd = trimEnd
      // 更新持续时间
      clip.duration = trimEnd - trimStart
      return true
    }
    return false
  }

  /**
   * 确保AudioContext处于运行状态 - 优化线上环境兼容性
   */
  public async ensureAudioContextRunning(): Promise<void> {
    if (!this.audioContext) {
      return
    }

    if (this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume()
      } catch (error) {
        // 尝试重新创建AudioContext
        await this.recreateAudioContext()
      }
    } else if (this.audioContext.state === 'closed') {
      await this.recreateAudioContext()
    }
  }

  /**
   * 重新创建AudioContext（用于线上环境的兼容性）
   */
  private async recreateAudioContext(): Promise<void> {
    try {
      // 创建新的AudioContext
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (!AudioContextClass) {
        throw new Error('AudioContext not supported')
      }

      const newAudioContext = new AudioContextClass()

      // 如果新的AudioContext是suspended状态，尝试resume
      if (newAudioContext.state === 'suspended') {
        await newAudioContext.resume()
      }

      // 重新创建节点
      const newMasterGainNode = newAudioContext.createGain()
      const newDestination = newAudioContext.createMediaStreamDestination()

      // 连接节点
      newMasterGainNode.connect(newDestination)
      newMasterGainNode.connect(newAudioContext.destination)

      // 更新引用
      this.audioContext = newAudioContext
      this.masterGainNode = newMasterGainNode
      this.destination = newDestination

      // 重新创建所有音频片段的节点
      this.recreateAllAudioNodes()
    } catch (error) {}
  }

  /**
   * 重新创建所有音频片段的节点
   */
  private recreateAllAudioNodes(): void {
    if (!this.audioContext) return

    // 清除旧的节点映射
    this.sourceNodes.clear()
    this.gainNodes.clear()
    this.effectNodes.clear()

    // 为每个音频片段重新创建节点
    this.clips.forEach((clip) => {
      try {
        this.createAudioNodes(clip)
      } catch (error) {}
    })
  }

  /**
   * 销毁音频混合器
   */
  public destroy(): void {
    // 断开所有连接
    this.clips.forEach((clip) => {
      this.disconnectAudioNodes(clip.id)
    })

    // 关闭音频上下文
    if (this.audioContext.state !== 'closed') {
      this.audioContext.close()
    }

    this.clips = []
    this.sourceNodes.clear()
    this.gainNodes.clear()
    this.effectNodes.clear()
  }

  /**
   * 创建纯净的音频流（不依赖原始视频音频）
   * 用于测试和确保有音频输出
   */
  public createPureAudioStream(): MediaStream | null {
    if (!this.audioContext) {
      return null
    }

    try {
      // 创建一个新的destination
      const pureDestination = this.audioContext.createMediaStreamDestination()

      // 创建一个可听到的测试音调
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()

      // 设置为440Hz的正弦波，音量很低
      oscillator.frequency.value = 440
      oscillator.type = 'sine'
      gainNode.gain.value = 0.01 // 很低的音量

      // 连接节点
      oscillator.connect(gainNode)
      gainNode.connect(pureDestination)

      // 开始生成音频
      oscillator.start()

      const stream = pureDestination.stream

      return stream
    } catch (error) {
      return null
    }
  }

  /**
   * 启用增强音频处理
   */
  public async enableEnhancedProcessing(): Promise<void> {
    try {
      // 初始化增强处理器
      this.enhancedProcessor = new EnhancedAudioProcessor()
      await this.enhancedProcessor.initialize()

      // 初始化缓冲管理器
      this.bufferManager = new AudioBufferManager(this.audioContext)

      // 初始化质量检测器
      this.qualityDetector = new AudioQualityDetector(this.audioContext)

      this.useEnhancedProcessing = true
    } catch (error) {
      throw error
    }
  }

  /**
   * 禁用增强音频处理
   */
  public disableEnhancedProcessing(): void {
    if (this.enhancedProcessor) {
      this.enhancedProcessor.dispose()
      this.enhancedProcessor = null
    }

    if (this.bufferManager) {
      this.bufferManager.dispose()
      this.bufferManager = null
    }

    if (this.qualityDetector) {
      this.qualityDetector.dispose()
      this.qualityDetector = null
    }

    this.useEnhancedProcessing = false
  }

  /**
   * 预加载音频片段（使用增强处理）
   */
  public async preloadAudioClips(clips: AudioClip[]): Promise<void> {
    if (!this.useEnhancedProcessing || !this.bufferManager) {
      return
    }

    try {
      const clipInfos: AudioClipInfo[] = clips.map((clip) => ({
        id: clip.id,
        url: clip.audioElement.src,
        startTime: clip.startTime,
        duration: clip.duration,
        trimStart: clip.trimStart,
        trimEnd: clip.trimEnd,
        volume: clip.volume,
        trackId: clip.trackId,
      }))

      await this.bufferManager.preloadMultipleClips(clipInfos)
    } catch (error) {}
  }

  /**
   * 智能预加载附近的音频片段
   */
  public async smartPreloadAudio(currentTime: number): Promise<void> {
    if (!this.useEnhancedProcessing || !this.bufferManager) {
      return
    }

    try {
      const clipInfos: AudioClipInfo[] = this.clips.map((clip) => ({
        id: clip.id,
        url: clip.audioElement.src,
        startTime: clip.startTime,
        duration: clip.duration,
        trimStart: clip.trimStart,
        trimEnd: clip.trimEnd,
        volume: clip.volume,
        trackId: clip.trackId,
      }))

      await this.bufferManager.smartPreload(clipInfos, currentTime)
    } catch (error) {}
  }

  /**
   * 分析音频质量
   */
  public async analyzeAudioQuality(duration: number = 5): Promise<any> {
    if (!this.useEnhancedProcessing || !this.qualityDetector) {
      return null
    }

    try {
      const outputStream = this.getOutputStream()
      if (!outputStream) {
        throw new Error('无法获取音频输出流')
      }

      const report = await this.qualityDetector.analyzeAudioStream(outputStream, duration)

      return report
    } catch (error) {
      return null
    }
  }

  /**
   * 获取增强处理器的输出流
   */
  public getEnhancedOutputStream(): MediaStream | null {
    if (!this.useEnhancedProcessing || !this.enhancedProcessor) {
      return null
    }

    return this.enhancedProcessor.getOutputStream()
  }

  /**
   * 获取所有音频片段
   */
  public getAllClips(): AudioClip[] {
    return [...this.clips]
  }
}
